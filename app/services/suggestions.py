from dataclasses import dataclass
from functools import cached_property
import logging
from uuid import UUID

from fastapi import UploadFile

from constants.message import ConversationMessageIntention, SuggestedUserPrompt, SystemReplyType
from models import QualConversation
from schemas import ConversationData
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import UserMessageSerializer
from schemas.extracted_data import AggregatedData, ConversationState
from services.mixins import CombinedHistoryMixin


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class SuggestedPromptsGenerator(CombinedHistoryMixin):
    """Detector for conversation message suggested prompts."""

    conversation_id: UUID
    conversation_data: ConversationData
    user_message: UserMessageSerializer
    intention: ConversationMessageIntention
    current_reply_type: SystemReplyType
    called_from: str
    files: list[UploadFile] | None = None
    dash_task_activity_id: int | None = None

    @property
    def aggregated_data(self) -> AggregatedData:
        return self.conversation_data.aggregated_data

    @property
    def confirmed_data(self) -> ConfirmedData:
        return self.conversation_data.confirmed_data

    @property
    def conversation(self) -> QualConversation:
        return self.conversation_data.conversation

    @property
    def user_provide_input_or_client(self):
        return bool(self._cleaned_user_message) or bool(self.files) or bool(self.aggregated_data.client_name)

    @property
    def user_provide_input_or_ldmf_country(self):
        return bool(self._cleaned_user_message) or bool(self.files) or bool(self.aggregated_data.ldmf_country)

    async def run(self) -> list[SuggestedUserPrompt]:
        """Run the suggested prompts generator."""
        logger.info(
            f'SuggestedPromptsGenerator was called from {self.called_from} with reply type {self.current_reply_type}. '
        )

        aggregated_data = self.aggregated_data
        confirmed_data = self.confirmed_data
        current_reply_type = self.current_reply_type

        # Use current_reply_type if provided, otherwise fall back to conversation history
        latest_system_message_type = self._latest_system_message_type or current_reply_type
        user_message: SuggestedUserPrompt | None = self.user_message.as_suggested_reply

        user_selects_discard_dash = self.intention == ConversationMessageIntention.DASH_DISCARD
        user_promts_to_discard_dash = self.intention in (
            ConversationMessageIntention.USER_DENIAL,
            ConversationMessageIntention.GENERATE_QUAL,
        )
        is_welcome = current_reply_type == SystemReplyType.WELCOME_MESSAGE
        if user_selects_discard_dash or user_promts_to_discard_dash and is_welcome:
            return [SuggestedUserPrompt.UPLOAD_DOCUMENT, SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION]

        suggested_prompts = []

        ##################
        # USE CASE 3, 4, 5 - Show me an example prompt
        user_selects_brief_description = self.intention == ConversationMessageIntention.UNCERTAINTY
        if user_selects_brief_description and current_reply_type == SystemReplyType.BRIEF_DESCRIPTION:
            # 1. GIVEN the user is on the Prompt page
            # 2. WHEN the user selects the option "Write a brief description"
            # 3. VERIFY that the user can see the "Show me an example prompt" as a clickable option under the message
            suggested_prompts.append(SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT)

        ##################
        # USE CASE 6, 7 - No, create my qual
        if (
            confirmed_data.required_fields_are_complete
            and confirmed_data.client_name
            and current_reply_type in SystemReplyType.get_data_complete_replies()
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.NO_CREATE_MY_QUAL)
            and user_message != SuggestedUserPrompt.NO_CREATE_MY_QUAL
            and str(self.conversation.State)
            not in [
                str(ConversationState.QUAL_CREATED),
            ]
        ):
            # 1. GIVEN the user is on the Prompt page
            # 2. WHEN the user provides relevant info for all required fields to generate the qual
            # 3. VERIFY that the user can see the "No, create my qual" suggested prompt above the input
            # Extended conditions:

            # 1. All required fields are provided
            # 2. Client name is confirmed
            # 3. Previous system message was asking for additional data
            # 4. Prompt hasn't been used before
            # 5. Current user message is not "No, create my qual"
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_MY_QUAL)

        ##################
        # USE CASE 8, 9 - Enter a new client
        # Use case: Suggest "Enter a new client" when multiple client name variations are detected, but not in the case
        # but not when user prompts a message rejecting rejects to chose clients options
        is_relevant_system_message = latest_system_message_type in [
            SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
        ] or current_reply_type in [SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS]
        is_prompt_unused = self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)
        is_not_current_user_message = user_message != SuggestedUserPrompt.ENTER_A_NEW_CLIENT
        is_client_name_already_picked = confirmed_data.client_name is not None
        if (
            self.user_provide_input_or_client
            and aggregated_data.detected_multiple_client_name_variations
            and is_relevant_system_message
            and is_prompt_unused
            and is_not_current_user_message
            and not is_client_name_already_picked
            and self.intention != ConversationMessageIntention.USER_DENIAL
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI detects a few variations of the Client name
            # 4. VERIFY that the user can see the "Enter a new client" suggested prompt above the input

            # Extended conditions:
            # 1. User provides a prompt/uploads a document
            # 2. AI detected multiple client name variations
            # 3. Previous system message was about multiple client name variations
            # 4. Prompt hasn't been used before
            # 5. Current user message is not "Enter a new client"
            suggested_prompts.append(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)

        ##################
        # USE CASE 10, 11 - Yes, this is correct
        if (
            self.user_provide_input_or_client
            and (aggregated_data.retrieves_single_client_name or confirmed_data.proposed_client_name_is_valid)
            and current_reply_type
            in [
                SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
            ]
            and user_message != SuggestedUserPrompt.YES_THIS_IS_CORRECT
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI retrieves a single result of the client's name
            # 4. VERIFY that the user can see the "Yes, this is correct" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.YES_THIS_IS_CORRECT)

        ##################
        # USE CASE 14, 15 - Yes (for new client)
        if (
            self.user_provide_input_or_client
            and not confirmed_data.client_name
            and current_reply_type
            in [
                SystemReplyType.CLIENT_NOT_FOUND,
            ]
            and user_message != SuggestedUserPrompt.YES
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI couldn't find the provided client name in the system (unique)
            # 4. VERIFY that the user can see the "Yes" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.YES)

        ##################
        # USE CASE 12, 13 - No, I'll enter the client name
        if (
            self.user_provide_input_or_client
            and (aggregated_data.retrieves_single_client_name or aggregated_data.couldnt_find_provided_client_name)
            and current_reply_type
            in [
                SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
                SystemReplyType.CLIENT_NOT_FOUND,
            ]
            and user_message
            not in [SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME, SuggestedUserPrompt.YES_THIS_IS_CORRECT]
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI retrieves a single result of the client's name
            # 4. OR AI couldn't find the provided client name in the system
            # 5. VERIFY that the user can see the "No, I'll enter the client name" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)

        ##################
        # USE CASE 16, 17 - No, I'll enter the Lead Deloitte Member Firm
        is_ldmf_relevant_system_message = (
            latest_system_message_type == SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
            or current_reply_type == SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
        )
        is_ldmf_prompt_unused = self._suggested_prompt_wasnt_used_yet(
            SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM
        )
        is_not_current_ldmf_user_message = user_message != SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM
        is_not_confirmed_fields_ready = current_reply_type != SystemReplyType.CONFIRMED_FIELDS_READY

        if (
            self.user_provide_input_or_ldmf_country
            and (
                aggregated_data.retrieves_single_ldmf_country
                or aggregated_data.detected_multiple_ldmf_country_variations
            )
            and is_ldmf_relevant_system_message
            and is_ldmf_prompt_unused
            and is_not_current_ldmf_user_message
            and is_not_confirmed_fields_ready
            and self.intention != ConversationMessageIntention.USER_DENIAL
            and not confirmed_data.ldmf_country
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. WHEN the user provides a prompt/uploads a document containing one
            # OR several possible Lead Deloitte Member Firm values
            # 3. VERIFY that the user can see the "No, I'll enter the Lead Deloitte Member Firm" suggested prompt above the input
            # except user denial in prompt like "No" or "I'll enter LDMF"
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM)

        ##################
        # Handle UNDEFINED responses for misspelled countries
        # When user enters a misspelled country and system returns UNDEFINED,
        # suggest "No, I'll enter the Lead Deloitte Member Firm"
        if (
            current_reply_type == SystemReplyType.UNDEFINED
            and self.user_provide_input_or_ldmf_country
            and is_ldmf_prompt_unused
            and is_not_current_ldmf_user_message
            and self.intention != ConversationMessageIntention.USER_DENIAL
            and str(self.conversation.State)
            not in [
                str(ConversationState.INITIAL),
            ]
            and not confirmed_data.ldmf_country
        ):
            # When system returns UNDEFINED (misspelled country), suggest manual LDMF input
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM)

        # tested case when a lot of non existant clients extracted, and user picks 1 client which is not existing too.
        if (
            self.user_provide_input_or_client
            and (
                aggregated_data.retrieves_single_client_name or aggregated_data.detected_multiple_client_name_variations
            )
            and latest_system_message_type
            in [
                SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE,
                SystemReplyType.CLIENT_NAME_TOO_LONG,
            ]
            and current_reply_type
            in [
                SystemReplyType.CLIENT_NOT_FOUND,
            ]
        ):
            suggested_prompts.append(SuggestedUserPrompt.YES)
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)

        # get only unique suggested prompts, keeping an order.
        suggested_prompts = list(dict.fromkeys(suggested_prompts))
        return suggested_prompts

    ################
    # utils & calculated properties

    @cached_property
    def _cleaned_user_message(self) -> str:
        """Get the cleaned user message."""
        return self.user_message.content.strip()

    @cached_property
    def _suggested_prompts_used_in_conversation(self) -> set[SuggestedUserPrompt]:
        """Get the suggested prompts used in the conversation."""
        used_prompts = set()

        if not self.conversation_message_history:
            return used_prompts

        for combined_message in self.conversation_message_history:
            as_suggested_reply: SuggestedUserPrompt | None = combined_message.user.as_suggested_reply
            if as_suggested_reply:
                used_prompts.add(as_suggested_reply)

        return used_prompts

    def _suggested_prompt_wasnt_used_yet(self, prompt: SuggestedUserPrompt) -> bool:
        """Check if a suggested prompt was used in the current conversation."""

        if prompt in self._suggested_prompts_used_in_conversation:
            return False

        return True
