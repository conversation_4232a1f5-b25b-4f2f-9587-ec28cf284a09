from enum import StrEnum


__all__ = [
    'OrchestratorName',
    'ActivityName',
    'ProcessingStatus',
    'EventType',
    'ExctractStatus',
    'OrchestratorInputType',
]


class OrchestratorName(StrEnum):
    DocumentProcessing = 'DocumentProcessingOrchestrator'
    UnifiedProcessing = 'UnifiedProcessingOrchestrator'


class ActivityName(StrEnum):
    ExtractDocumentText = 'ExtractDocumentTextActivity'
    ChunkDocument = 'ChunkDocumentActivity'
    UpdateProcessingStatus = 'UpdateProcessingStatusActivity'
    SendNotification = 'SendNotificationActivity'
    SendQueueMessage = 'SendQueueMessageActivity'
    ReadPrompt = 'ReadPromptActivity'
    ExtractData = 'ExtractDataActivity'
    MergeExtractionData = 'MergeExtractionDataActivity'
    SaveExtractionData = 'SaveExtractionDataActivity'
    AggregateMultiSourceData = 'AggregateMultiSourceDataActivity'
    SaveAggregatedResultsToBlob = 'SaveAggregatedResultsToBlobActivity'
    SendFinalQueueMessage = 'SendFinalQueueMessageActivity'


class ProcessingStatus(StrEnum):
    DocumentExtractionStarted = 'DocumentExtractionStarted'
    DocumentIsCorrupted = 'DocumentIsCorrupted'
    DocumentExtractionFailed = 'DocumentExtractionFailed'
    DocumentExtractionCompleted = 'DocumentExtractionCompleted'
    DocumentChunkingCompleted = 'DocumentChunkingCompleted'
    DocumentRequiredFieldsExtracted = 'DocumentRequiredFieldsExtracted'

    DocumentProcessingFailed = 'DocumentProcessingFailed'

    PromptProcessingStarted = 'PromptProcessingStarted'
    PromptProcessingFailed = 'PromptProcessingFailed'
    PromptProcessingCompleted = 'PromptProcessingCompleted'
    PromptChunkingCompleted = 'PromptChunkingCompleted'
    PromptRequiredFieldsExtracted = 'PromptRequiredFieldsExtracted'

    OrchestratorDocumentProcessingFailed = 'OrchestratorDocumentProcessingFailed'
    OrchestratorDocumentProcessingCompleted = 'OrchestratorDocumentProcessingCompleted'

    # Unified processing statuses
    UnifiedProcessingStarted = 'UnifiedProcessingStarted'
    UnifiedProcessingCompleted = 'UnifiedProcessingCompleted'
    UnifiedProcessingFailed = 'UnifiedProcessingFailed'
    MultiSourceDataAggregated = 'MultiSourceDataAggregated'
    FinalResultsSavedToBlob = 'FinalResultsSavedToBlob'


class EventType(StrEnum):
    DocumentExtractionStarted = 'document_extraction_started'
    DocumentExtractionFailed = 'document_extraction_failed'
    DocumentChunkingCompleted = 'document_chunking_completed'

    PromptProcessingStarted = 'prompt_processing_started'
    PromptProcessingFailed = 'prompt_processing_failed'
    PromptProcessingCompleted = 'prompt_processing_completed'
    PromptChunkingCompleted = 'prompt_chunking_completed'

    RequiredFieldsExtracted = 'required_fields_extracted'  # single event for docs and prompts

    DocumentProcessingFailed = 'document_processing_failed'
    DocumentIsCorruptedError = 'document_is_corrupted_error'

    # Unified processing events
    UnifiedProcessingStarted = 'unified_processing_started'
    UnifiedProcessingCompleted = 'unified_processing_completed'
    UnifiedProcessingFailed = 'unified_processing_failed'
    UnifiedProcessingError = 'unified_processing_error'  # When all files in batch are corrupted
    MultiSourceDataAggregated = 'multi_source_data_aggregated'


class ExctractStatus(StrEnum):
    Success = 'success'
    Failed = 'failed'


class OrchestratorInputType(StrEnum):
    Document = 'document'
    Prompt = 'text-prompt'
    Unified = 'unified'
