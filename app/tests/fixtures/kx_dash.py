from unittest.mock import AsyncMock, patch

import pytest

from repositories.kx_dash import KXDashRepository
from schemas import DashTaskResponse
from schemas.dates import DatesLLMResponse
from services.kx_dash import KXDashService


__all__ = [
    'mock_activity',
    'mock_activities_response',
    'kx_dash_service_with_repo',
    'kx_dash_repository',
    'extracted_data_service_mock',
    'mock_kx_dash_service',
]


@pytest.fixture(autouse=True)
def mock_kx_dash_service():
    """Auto-use fixture that mocks KX Dash service to prevent network calls."""
    with patch('services.kx_dash.KXDashService.list') as mock_list:
        # Return empty list by default - tests can override this if needed
        mock_list.return_value = []
        yield mock_list


@pytest.fixture
def kx_dash_repository():
    """Mock KX Dash repository"""
    mock = AsyncMock()

    # Configure mock to return actual data structures instead of AsyncMock objects
    mock.MOCKED_TASKS = KXDashRepository.MOCKED_TASKS
    mock.MOCKED_LIST_TASKS = KXDashRepository.MOCKED_LIST_TASKS
    mock._NEED_LLM_DATE_VALIDATION_KEY = KXDashRepository._NEED_LLM_DATE_VALIDATION_KEY

    # Configure list method to return list of activity dictionaries
    mock.list = AsyncMock(return_value=list(KXDashRepository.MOCKED_TASKS.values()))

    # Configure get method to return single activity dictionary based on ID
    mock.get = AsyncMock(side_effect=lambda activity_id, token: KXDashRepository.MOCKED_TASKS.get(activity_id))

    return mock


@pytest.fixture
def extracted_data_service_mock():
    """Mock extracted data service"""
    mock = AsyncMock()
    mock.list = AsyncMock(return_value=[])
    mock.update = AsyncMock()
    mock.aggregate_data = AsyncMock()
    mock.list_dash_tasks = AsyncMock(return_value=[])
    return mock


@pytest.fixture
def kx_dash_service_with_repo(kx_dash_repository, extracted_data_service_mock):
    """KX Dash service with mocked repository and extracted data service"""
    # Mock the date validator service to fix problematic dates
    date_validator_mock = AsyncMock()

    # Configure date validator to fix known problematic dates
    def mock_find_dates(user_message: str):
        if 'Februart 1, 2024' in user_message:
            # Fix the typo and return proper ISO format dates
            return DatesLLMResponse(engagement_start_date='2024-02-01', engagement_end_date='2024-11-30')  # type: ignore
        elif 'March 10 - September 30, 2024' in user_message:
            # Add missing year to March 10
            return DatesLLMResponse(engagement_start_date='2024-03-10', engagement_end_date='2024-09-30')  # type: ignore
        elif '01/01/2024 - 31/12/2024' in user_message:
            # Handle ambiguous format (assume day/month/year)
            return DatesLLMResponse(engagement_start_date='2024-01-01', engagement_end_date='2024-12-31')  # type: ignore
        else:
            # Default fallback - return None dates
            return DatesLLMResponse(engagement_start_date=None, engagement_end_date=None)

    date_validator_mock.find_dates = AsyncMock(side_effect=mock_find_dates)

    service = KXDashService(
        kx_dash_repository=kx_dash_repository,
        extracted_data_service=extracted_data_service_mock,
        date_validator_service=date_validator_mock,
    )

    # Configure the extracted_data_service_mock behavior
    extracted_data_service_mock.aggregate_data.return_value = None
    extracted_data_service_mock.update.return_value = None

    return service


@pytest.fixture
def mock_activity():
    """Fixture providing mock activity data"""
    return {
        'activityId': 100001,
        'activityName': 'Mock Quality Review 100001',
        'clientName': 'Mock Client Corporation',
        'memberFirm': 'US',
        'country': 'United States',
        'globalBusiness': 'Audit & Assurance',
        'globalBusinessServiceArea': 'Audit',
        'globalBusinessServiceLine': 'External Audit',
        'globalIndustry': 'Financial Services',
        'globalIndustrySector': 'Banking',
        'engagementCode': 'ENG-100001',
        'globalLCSPEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementLepEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementManagerEmails': ['<EMAIL>', '<EMAIL>'],
        'activityOwnerEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementStartDate': '2024-04-01',
        'engagementEndDate': '2024-06-30',
        'dueDate': '2025-04-19',
    }


@pytest.fixture
def mock_activities_response(mock_activity):
    """Fixture providing mock activities list response"""
    return [DashTaskResponse.model_validate(mock_activity)]
